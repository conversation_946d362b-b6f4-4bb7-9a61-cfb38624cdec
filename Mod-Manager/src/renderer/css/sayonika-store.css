/* Sayonika Store Styles */

.sayonika-store {
    padding: 20px;
    max-width: 1200px;
    margin: 0 auto;
    height: 100%;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
}

/* Header Section */
.store-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 2px solid #333;
    flex-shrink: 0;
}

/* Scrollable Content Area */
.store-content {
    flex: 1;
    overflow-y: auto;
    min-height: 0;
    padding-right: 5px; /* Add some space for scrollbar */
    padding-bottom: 20px; /* Add bottom padding for better scrolling */
}

/* Custom scrollbar for store content */
.store-content::-webkit-scrollbar {
    width: 8px;
}

.store-content::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
}

.store-content::-webkit-scrollbar-thumb {
    background: rgba(255, 105, 180, 0.6);
    border-radius: 4px;
    transition: background 0.2s ease;
}

.store-content::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 105, 180, 0.8);
}

.store-title h1 {
    margin: 0;
    color: #ff69b4;
    font-size: 2.5em;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
}

.store-title p {
    margin: 5px 0 0 0;
    color: #ccc;
    font-size: 1.1em;
}

.store-actions {
    display: flex;
    align-items: center;
    gap: 20px;
}

/* Search Container */
.search-container {
    position: relative;
}

.search-input {
    padding: 10px 40px 10px 15px;
    border: 2px solid #444;
    border-radius: 25px;
    background: #222;
    color: #fff;
    font-size: 14px;
    width: 250px;
    transition: border-color 0.3s ease;
}

.search-input:focus {
    outline: none;
    border-color: #ff69b4;
}

.search-icon {
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: #888;
}

/* Authentication Section */
.auth-section {
    display: flex;
    align-items: center;
}



.user-info {
    display: flex;
    align-items: center;
    gap: 10px;
    background: #333;
    padding: 8px 15px;
    border-radius: 20px;
}

.user-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    border: 2px solid #ff69b4;
    object-fit: cover;
    background: #333;
    display: block;
}

.username {
    color: #fff;
    font-weight: bold;
}

.btn-logout {
    background: #666;
    color: white;
    border: none;
    padding: 5px 10px;
    border-radius: 15px;
    cursor: pointer;
    transition: background 0.2s ease;
}

.btn-logout:hover {
    background: #888;
}

/* Filters Section */
.store-filters {
    display: flex;
    gap: 20px;
    margin-bottom: 30px;
    padding: 15px;
    background: #222;
    border-radius: 10px;
    flex-wrap: wrap;
}

.filter-group {
    display: flex;
    align-items: center;
    gap: 8px;
}

.filter-group label {
    color: #ccc;
    font-weight: bold;
}

.filter-group select {
    padding: 8px 12px;
    border: 1px solid #444;
    border-radius: 5px;
    background: #333;
    color: #fff;
    cursor: pointer;
}

.filter-group input[type="checkbox"] {
    margin-right: 5px;
}

/* Loading and Error States */
.loading-container, .error-container {
    text-align: center;
    padding: 60px 20px;
    color: #ccc;
}

.loading-container i {
    font-size: 3em;
    color: #ff69b4;
    margin-bottom: 20px;
}

.error-container i {
    font-size: 3em;
    color: #ff4444;
    margin-bottom: 20px;
}

.btn-retry {
    background: #ff69b4;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 5px;
    cursor: pointer;
    margin-top: 15px;
}

/* Mods Grid */
.mods-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.no-mods {
    grid-column: 1 / -1;
    text-align: center;
    padding: 60px 20px;
    color: #888;
}

.no-mods i {
    font-size: 3em;
    margin-bottom: 20px;
}

/* Mod Cards */
.mod-card {
    background: #222;
    border-radius: 10px;
    overflow: hidden;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
    cursor: pointer;
    border: 2px solid transparent;
}

.mod-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(255, 105, 180, 0.3);
    border-color: #ff69b4;
}

.mod-thumbnail {
    position: relative;
    height: 180px;
    overflow: hidden;
}

.mod-thumbnail img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.mod-card:hover .mod-thumbnail img {
    transform: scale(1.05);
}

.featured-badge, .nsfw-badge {
    position: absolute;
    top: 10px;
    right: 10px;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: bold;
    text-transform: uppercase;
}

.featured-badge {
    background: linear-gradient(45deg, #ffd700, #ffed4e);
    color: #333;
}

.nsfw-badge {
    background: linear-gradient(45deg, #ff4444, #cc0000);
    color: white;
}

.mod-info {
    padding: 15px;
}

.mod-title {
    margin: 0 0 8px 0;
    color: #fff;
    font-size: 1.2em;
    font-weight: bold;
}

.mod-author {
    margin: 0 0 10px 0;
    color: #ff69b4;
    font-size: 0.9em;
}

.mod-description {
    margin: 0 0 15px 0;
    color: #ccc;
    font-size: 0.9em;
    line-height: 1.4;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.mod-stats {
    display: flex;
    gap: 15px;
    margin-bottom: 10px;
    font-size: 0.8em;
    color: #888;
}

.mod-stats span {
    display: flex;
    align-items: center;
    gap: 4px;
}

.mod-tags {
    display: flex;
    gap: 5px;
    flex-wrap: wrap;
    margin-bottom: 15px;
}

.tag {
    background: #444;
    color: #ccc;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 0.8em;
}

.mod-actions {
    padding: 0 15px 15px 15px;
}

.btn-download {
    width: 100%;
    background: linear-gradient(45deg, #ff69b4, #ff1493);
    color: white;
    border: none;
    padding: 10px;
    border-radius: 5px;
    cursor: pointer;
    font-weight: bold;
    transition: opacity 0.2s ease;
}

.btn-download:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.btn-download:not(:disabled):hover {
    background: linear-gradient(45deg, #ff1493, #ff69b4);
}

/* Pagination */
.pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 20px;
    margin-top: 30px;
}

.pagination button {
    background: #333;
    color: #fff;
    border: 1px solid #555;
    padding: 10px 15px;
    border-radius: 5px;
    cursor: pointer;
    transition: background 0.2s ease;
}

.pagination button:not(:disabled):hover {
    background: #555;
}

.pagination button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.page-info {
    color: #ccc;
    font-weight: bold;
}

/* Modal Styles */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    backdrop-filter: blur(5px);
}

.modal-content {
    background: #222;
    border-radius: 10px;
    max-width: 500px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
    border: 2px solid #444;
}

.mod-details-modal {
    max-width: 800px;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid #444;
}

.modal-header h2 {
    margin: 0;
    color: #fff;
}

.modal-close {
    background: none;
    border: none;
    color: #888;
    font-size: 1.5em;
    cursor: pointer;
    padding: 5px;
    border-radius: 50%;
    transition: background 0.2s ease;
}

.modal-close:hover {
    background: #444;
    color: #fff;
}

.modal-body {
    padding: 20px;
}

/* Login Modal */
.login-options {
    display: flex;
    flex-direction: column;
    gap: 10px;
    margin-bottom: 20px;
}

.btn-oauth {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    padding: 12px 20px;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-weight: bold;
    transition: transform 0.2s ease;
}

.btn-oauth:hover {
    transform: translateY(-2px);
}

.btn-oauth.discord {
    background: #5865f2;
    color: white;
}

.btn-oauth.github {
    background: #333;
    color: white;
}

.divider {
    text-align: center;
    margin: 20px 0;
    position: relative;
    color: #888;
}

.divider::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 1px;
    background: #444;
}

.divider span {
    background: #222;
    padding: 0 15px;
    position: relative;
}

.credential-login {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.form-group label {
    color: #ccc;
    font-weight: bold;
}

.form-group input[type="text"],
.form-group input[type="email"],
.form-group input[type="password"] {
    padding: 10px;
    border: 1px solid #444;
    border-radius: 5px;
    background: #333;
    color: #fff;
    font-size: 14px;
    transition: border-color 0.3s ease;
}

.form-group input[type="text"]:focus,
.form-group input[type="email"]:focus,
.form-group input[type="password"]:focus {
    outline: none;
    border-color: #ff69b4;
    box-shadow: 0 0 5px rgba(255, 105, 180, 0.3);
}

.input-help {
    color: #888;
    font-size: 12px;
    margin-top: 4px;
    font-style: italic;
}

.btn-submit {
    background: linear-gradient(45deg, #ff69b4, #ff1493);
    color: white;
    border: none;
    padding: 12px;
    border-radius: 5px;
    cursor: pointer;
    font-weight: bold;
    transition: opacity 0.2s ease;
}

.btn-submit:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.register-link {
    text-align: center;
    margin-top: 15px;
}

.register-link a {
    color: #ff69b4;
    text-decoration: none;
}

.register-link a:hover {
    text-decoration: underline;
}

/* Mod Details Modal */
.mod-details-content {
    display: flex;
    gap: 30px;
}

.mod-details-left {
    flex: 0 0 300px;
}

.mod-details-right {
    flex: 1;
}

.mod-details-image {
    width: 100%;
    border-radius: 10px;
    margin-bottom: 20px;
}

.mod-details-stats {
    background: #333;
    padding: 15px;
    border-radius: 10px;
    margin-bottom: 20px;
}

.stat {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 10px;
    color: #ccc;
}

.stat:last-child {
    margin-bottom: 0;
}

.stat i {
    color: #ff69b4;
    width: 16px;
}

.btn-download-large {
    width: 100%;
    background: linear-gradient(45deg, #ff69b4, #ff1493);
    color: white;
    border: none;
    padding: 15px;
    border-radius: 10px;
    cursor: pointer;
    font-weight: bold;
    font-size: 1.1em;
    transition: opacity 0.2s ease;
}

.btn-download-large:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.mod-description h3,
.mod-tags-section h3,
.mod-requirements h3 {
    color: #fff;
    margin-bottom: 10px;
    border-bottom: 1px solid #444;
    padding-bottom: 5px;
}

.mod-description div {
    color: #ccc;
    line-height: 1.6;
}

.tags-list {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

.mod-requirements ul {
    color: #ccc;
    margin: 0;
    padding-left: 20px;
}

/* Screenshots Section */
.mod-screenshots-section {
    margin: 20px 0;
}

.mod-screenshots-section h3 {
    color: #ff69b4;
    margin-bottom: 15px;
    font-size: 1.2em;
}

.mod-screenshots-gallery {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    gap: 10px;
    margin-bottom: 20px;
}

.screenshot-thumbnail {
    position: relative;
    aspect-ratio: 16/9;
    border-radius: 8px;
    overflow: hidden;
    cursor: pointer;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
    border: 2px solid transparent;
}

.screenshot-thumbnail:hover {
    transform: scale(1.05);
    box-shadow: 0 5px 15px rgba(255, 105, 180, 0.3);
    border-color: #ff69b4;
}

.screenshot-thumbnail img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: opacity 0.2s ease;
}

.screenshot-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.2s ease;
}

.screenshot-thumbnail:hover .screenshot-overlay {
    opacity: 1;
}

.screenshot-overlay i {
    color: #ff69b4;
    font-size: 1.5em;
}

/* Screenshot Modal */
.screenshot-modal-overlay {
    background: rgba(0, 0, 0, 0.95);
    z-index: 2000;
}

.screenshot-modal-content {
    max-width: 90vw;
    max-height: 90vh;
    width: auto;
    background: #1a1a1a;
    border: 2px solid #ff69b4;
}

.screenshot-modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    border-bottom: 1px solid #333;
    background: #222;
}

.screenshot-modal-header h3 {
    color: #ff69b4;
    margin: 0;
    font-size: 1.1em;
}

.screenshot-modal-body {
    padding: 0;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.screenshot-navigation {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
}

.screenshot-modal-image {
    max-width: 100%;
    max-height: 70vh;
    object-fit: contain;
    display: block;
}

.screenshot-nav-btn {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    background: rgba(255, 105, 180, 0.8);
    color: white;
    border: none;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    cursor: pointer;
    font-size: 1.2em;
    transition: background 0.2s ease, opacity 0.2s ease;
    z-index: 10;
}

.screenshot-nav-btn:hover:not(:disabled) {
    background: rgba(255, 105, 180, 1);
}

.screenshot-nav-btn:disabled {
    opacity: 0.3;
    cursor: not-allowed;
}

.screenshot-nav-btn.prev {
    left: 20px;
}

.screenshot-nav-btn.next {
    right: 20px;
}

.screenshot-indicators {
    display: flex;
    gap: 8px;
    padding: 15px;
    justify-content: center;
}

.screenshot-indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: #555;
    cursor: pointer;
    transition: background 0.2s ease, transform 0.2s ease;
}

.screenshot-indicator:hover {
    background: #ff69b4;
    transform: scale(1.2);
}

.screenshot-indicator.active {
    background: #ff69b4;
    transform: scale(1.3);
}

/* Responsive Design */
@media (max-width: 768px) {
    .store-header {
        flex-direction: column;
        gap: 20px;
        align-items: stretch;
    }

    .store-actions {
        flex-direction: column;
        gap: 15px;
    }

    .search-input {
        width: 100%;
    }

    .store-filters {
        flex-direction: column;
        gap: 15px;
    }

    .mods-grid {
        grid-template-columns: 1fr;
    }

    .mod-details-content {
        flex-direction: column;
    }

    .mod-details-left {
        flex: none;
    }

    .modal-content {
        width: 95%;
        margin: 10px;
    }

    .mod-screenshots-gallery {
        grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
        gap: 8px;
    }

    .screenshot-modal-content {
        max-width: 95vw;
        max-height: 95vh;
    }

    .screenshot-nav-btn {
        width: 40px;
        height: 40px;
        font-size: 1em;
    }

    .screenshot-nav-btn.prev {
        left: 10px;
    }

    .screenshot-nav-btn.next {
        right: 10px;
    }

    .screenshot-modal-image {
        max-height: 60vh;
    }
}

/* NSFW Warning Modal Styles */
.nsfw-warning-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 10000;
}

.nsfw-warning-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    backdrop-filter: blur(5px);
}

.nsfw-warning-dialog {
    background: #222;
    border-radius: 12px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
    max-width: 500px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
    border: 2px solid #ff6b6b;
    animation: nsfwModalShow 0.3s ease-out;
}

@keyframes nsfwModalShow {
    from {
        opacity: 0;
        transform: scale(0.9);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

.nsfw-warning-header {
    padding: 20px 25px 15px;
    border-bottom: 1px solid #444;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: #333;
}

.nsfw-warning-header h3 {
    margin: 0;
    font-size: 1.25rem;
    font-weight: 600;
    color: #fff;
    display: flex;
    align-items: center;
    gap: 10px;
}

.nsfw-warning-header h3 i {
    color: #ff6b6b;
}

.nsfw-warning-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: #ccc;
    cursor: pointer;
    padding: 5px;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.nsfw-warning-close:hover {
    background: #444;
    color: #fff;
}

.nsfw-warning-body {
    padding: 25px;
    color: #fff;
    text-align: left;
}

.nsfw-warning-body p {
    margin-bottom: 15px;
    line-height: 1.6;
}

.nsfw-warning-body ul {
    margin: 15px 0;
    padding-left: 20px;
}

.nsfw-warning-body li {
    margin-bottom: 8px;
    line-height: 1.5;
}

.nsfw-warning-footer {
    padding: 15px 25px 25px;
    display: flex;
    gap: 10px;
    justify-content: flex-end;
    background: #333;
}

.nsfw-warning-footer .btn {
    padding: 10px 20px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-weight: 500;
    transition: all 0.2s ease;
    font-size: 14px;
}

.nsfw-warning-footer .btn-secondary {
    background: #6c757d;
    color: #fff;
}

.nsfw-warning-footer .btn-secondary:hover {
    background: #5a6268;
}

.nsfw-warning-footer .btn-danger {
    background: #dc3545;
    color: #fff;
}

.nsfw-warning-footer .btn-danger:hover {
    background: #c82333;
}

/* Enhanced NSFW badge for mod cards */
.mod-card.nsfw {
    border-left: 4px solid #ff6b6b;
    position: relative;
}

@keyframes nsfwPulse {
    0%, 100% {
        box-shadow: 0 2px 8px rgba(255, 107, 107, 0.3);
        transform: scale(1);
    }
    50% {
        box-shadow: 0 4px 16px rgba(255, 107, 107, 0.5);
        transform: scale(1.02);
    }
}

/* Responsive adjustments for NSFW modal */
@media (max-width: 576px) {
    .nsfw-warning-dialog {
        margin: 1rem;
        width: calc(100% - 2rem);
    }

    .nsfw-warning-header,
    .nsfw-warning-body,
    .nsfw-warning-footer {
        padding: 15px 20px;
    }

    .nsfw-warning-footer {
        flex-direction: column;
        gap: 8px;
    }

    .nsfw-warning-footer .btn {
        width: 100%;
    }
}

/* NSFW indicator in modal header */
.nsfw-indicator {
    background: linear-gradient(135deg, #ff6b6b, #ee5a52);
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: bold;
    margin-left: 10px;
    animation: nsfwPulse 2s ease-in-out infinite;
}

.nsfw-indicator i {
    font-size: 10px;
    margin-right: 4px;
}

# Nginx configuration for Sayonika with external URL support
# Place this in your nginx sites-available directory

server {
    listen 80;
    server_name your-domain.com;  # Replace with your actual domain
    
    # Redirect HTTP to HTTPS
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name your-domain.com;  # Replace with your actual domain
    
    # SSL configuration (replace with your actual certificate paths)
    ssl_certificate /path/to/your/certificate.crt;
    ssl_certificate_key /path/to/your/private.key;
    
    # SSL settings
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;
    
    # General settings
    client_max_body_size 2G;  # Allow large uploads (adjust as needed)
    client_body_timeout 300s;  # 5 minutes timeout for uploads
    client_header_timeout 60s;
    send_timeout 300s;
    
    # Proxy settings for Node app
    location / {
        proxy_pass http://localhost:3000;  # Adjust port if different
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        
        # Timeout settings
        proxy_connect_timeout 60s;
        proxy_send_timeout 300s;
        proxy_read_timeout 300s;
    }
    
    # Special handling for upload endpoints with larger limits
    location /api/mods {
        client_max_body_size 2G;  # Even larger limit for mod uploads
        client_body_timeout 600s;  # 10 minutes for very large uploads
        
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        
        # Extended timeouts for uploads
        proxy_connect_timeout 60s;
        proxy_send_timeout 600s;
        proxy_read_timeout 600s;
        
        # Buffer settings for large uploads
        proxy_request_buffering off;
        proxy_buffering off;
    }
    
    # Static file serving with caching
    location /uploads/ {
        alias /path/to/sayonika/uploads/;  # Adjust path to your uploads directory
        expires 1y;
        add_header Cache-Control "public, immutable";
        add_header X-Content-Type-Options nosniff;
    }
    
    location /css/ {
        alias /path/to/sayonika/public/css/;  # Adjust path to your CSS directory
        expires 1y;
        add_header Cache-Control "public, immutable";
        gzip_static on;
    }
    
    location /js/ {
        alias /path/to/sayonika/public/js/;  # Adjust path to your JS directory
        expires 1y;
        add_header Cache-Control "public, immutable";
        gzip_static on;
    }
    
    location /images/ {
        alias /path/to/sayonika/public/images/;  # Adjust path to your images directory
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
    
    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;
    
    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        image/svg+xml;
}
